#include "../src/GameEngine/GuildManager.h"
#include "../src/Common/Logger.h"
#include <iostream>
#include <cassert>

using namespace MirServer;

// 模拟PlayObject类用于测试
class MockPlayObject : public PlayObject {
public:
    MockPlayObject(const std::string& name) : m_charName(name), m_gold(100000) {
        m_humDataInfo.guildName = "";
        m_humDataInfo.guildRank = 0;
    }

    const std::string& GetCharName() const override { return m_charName; }
    const std::string& GetGuildName() const override { return m_humDataInfo.guildName; }
    BYTE GetGuildRank() const override { return m_humDataInfo.guildRank; }
    int GetGold() const override { return m_gold; }
    void DecGold(int amount) override { m_gold -= amount; }
    const HumDataInfo& GetHumDataInfo() const override { return m_humDataInfo; }

    void SendDefMessage(WORD msg, WORD param1, WORD param2, WORD param3, WORD param4) override {
        std::cout << "SendDefMessage: " << msg << std::endl;
    }

    void SendMessage(const std::string& message, int color) override {
        std::cout << "Message to " << m_charName << ": " << message << std::endl;
    }

private:
    std::string m_charName;
    int m_gold;
    HumDataInfo m_humDataInfo;
};

void TestGuildCreation() {
    std::cout << "\n=== Testing Guild Creation ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    guildManager.Initialize();

    auto chief = std::make_unique<MockPlayObject>("TestChief");

    // 测试创建行会
    bool result = guildManager.CreateGuild("TestGuild", chief.get());
    assert(result == true);
    std::cout << "✓ Guild creation successful" << std::endl;

    // 测试重复创建
    result = guildManager.CreateGuild("TestGuild", chief.get());
    assert(result == false);
    std::cout << "✓ Duplicate guild creation prevented" << std::endl;

    // 验证行会存在
    Guild* guild = guildManager.FindGuild("TestGuild");
    assert(guild != nullptr);
    assert(guild->GetGuildName() == "TestGuild");
    assert(guild->GetMemberCount() == 1);
    std::cout << "✓ Guild found and verified" << std::endl;
}

void TestGuildMembers() {
    std::cout << "\n=== Testing Guild Members ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("TestGuild");
    assert(guild != nullptr);

    auto member1 = std::make_unique<MockPlayObject>("Member1");
    auto member2 = std::make_unique<MockPlayObject>("Member2");

    // 测试添加成员
    bool result = guild->AddMember(member1.get(), GuildRank::MEMBER);
    assert(result == true);
    assert(guild->GetMemberCount() == 2);
    std::cout << "✓ Member added successfully" << std::endl;

    // 测试职位更新
    result = guild->UpdateMemberRank("Member1", GuildRank::CAPTAIN, "队长");
    assert(result == true);
    assert(guild->GetMemberRank("Member1") == GuildRank::CAPTAIN);
    std::cout << "✓ Member rank updated successfully" << std::endl;

    // 测试移除成员
    result = guild->RemoveMember("Member1");
    assert(result == true);
    assert(guild->GetMemberCount() == 1);
    std::cout << "✓ Member removed successfully" << std::endl;
}

void TestGuildDonation() {
    std::cout << "\n=== Testing Guild Donation ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("TestGuild");
    assert(guild != nullptr);

    auto donor = std::make_unique<MockPlayObject>("Donor");
    guild->AddMember(donor.get(), GuildRank::MEMBER);

    int initialGold = donor->GetGold();
    int donationAmount = 5000;

    // 测试金币捐献
    bool result = guild->DonateGold(donor.get(), donationAmount);
    assert(result == true);
    assert(donor->GetGold() == initialGold - donationAmount);
    assert(guild->GetGuildGold() == donationAmount);
    assert(guild->GetTotalDonation("Donor") == donationAmount);
    std::cout << "✓ Gold donation successful" << std::endl;

    // 测试物品捐献
    result = guild->DonateItem(donor.get(), "铁剑", 10);
    assert(result == true);
    std::cout << "✓ Item donation successful" << std::endl;
}

void TestGuildLeveling() {
    std::cout << "\n=== Testing Guild Leveling ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("TestGuild");
    assert(guild != nullptr);

    int initialLevel = guild->GetGuildLevel();

    // 添加经验
    guild->AddGuildExp(2000); // 足够升级的经验

    // 检查是否升级
    assert(guild->GetGuildLevel() > initialLevel);
    std::cout << "✓ Guild leveled up from " << initialLevel << " to " << guild->GetGuildLevel() << std::endl;
}

void TestGuildSkills() {
    std::cout << "\n=== Testing Guild Skills ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("TestGuild");
    assert(guild != nullptr);

    // 确保有足够金币
    guild->SetGuildGold(50000);

    // 测试学习技能
    bool result = guild->LearnGuildSkill("行会攻击力提升");
    assert(result == true);
    assert(guild->GetGuildSkillLevel("行会攻击力提升") == 1);
    std::cout << "✓ Guild skill learned successfully" << std::endl;

    // 测试升级技能
    result = guild->UpgradeGuildSkill("行会攻击力提升");
    assert(result == true);
    assert(guild->GetGuildSkillLevel("行会攻击力提升") == 2);
    std::cout << "✓ Guild skill upgraded successfully" << std::endl;
}

void TestGuildWarehouse() {
    std::cout << "\n=== Testing Guild Warehouse ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("TestGuild");
    assert(guild != nullptr);

    auto captain = std::make_unique<MockPlayObject>("Captain");
    guild->AddMember(captain.get(), GuildRank::CAPTAIN);

    // 测试存入物品
    bool result = guild->DepositItem(captain.get(), "金币", 1000);
    assert(result == true);
    std::cout << "✓ Item deposited to warehouse successfully" << std::endl;

    // 测试取出物品
    result = guild->WithdrawItem(captain.get(), "金币", 500);
    assert(result == true);
    std::cout << "✓ Item withdrawn from warehouse successfully" << std::endl;

    // 获取仓库物品列表
    auto items = guild->GetWarehouseItems();
    assert(!items.empty());
    std::cout << "✓ Warehouse items retrieved successfully" << std::endl;
}

void TestGuildRanking() {
    std::cout << "\n=== Testing Guild Ranking ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();

    // 创建另一个行会用于排名测试
    auto chief2 = std::make_unique<MockPlayObject>("Chief2");
    guildManager.CreateGuild("TestGuild2", chief2.get());

    // 更新排名
    guildManager.UpdateAllGuildRankings();

    // 获取排名
    auto rankings = guildManager.GetGuildRankings(10);
    assert(rankings.size() >= 2);
    std::cout << "✓ Guild rankings updated and retrieved successfully" << std::endl;

    // 检查排名
    int rank = guildManager.GetGuildRank("TestGuild");
    assert(rank > 0);
    std::cout << "✓ Guild rank retrieved: " << rank << std::endl;
}

void TestGuildConfiguration() {
    std::cout << "\n=== Testing Guild Configuration ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("TestGuild");
    assert(guild != nullptr);

    // 测试原项目兼容的基本属性
    int originalBuildPoint = guild->GetBuildPoint();
    int originalAurae = guild->GetAurae();

    // 修改基本属性
    guild->SetBuildPoint(1500);
    guild->SetAurae(800);
    guild->SetStability(900);
    guild->SetFlourishing(700);
    guild->SetChiefItemCount(60);

    // 保存配置文件
    guild->SaveToFile();

    // 重新加载配置文件
    guild->LoadFromFile();

    // 验证原项目兼容属性是否正确加载
    assert(guild->GetBuildPoint() == 1500);
    assert(guild->GetAurae() == 800);
    assert(guild->GetStability() == 900);
    assert(guild->GetFlourishing() == 700);
    assert(guild->GetChiefItemCount() == 60);
    std::cout << "✓ Original Delphi project compatible attributes work correctly" << std::endl;

    // 测试扩展配置功能
    guild->SetConfigBool("Permissions.AllowMemberInvite", true);
    guild->SetConfigInt("War.DefaultDuration", 7200000);
    guild->SetConfigString("Skills.UpgradeCostMultiplier", "1.5");

    // 测试读取扩展配置
    bool allowInvite = guild->GetConfigBool("Permissions.AllowMemberInvite");
    int warDuration = guild->GetConfigInt("War.DefaultDuration");
    std::string costMultiplier = guild->GetConfigString("Skills.UpgradeCostMultiplier");

    assert(allowInvite == true);
    assert(warDuration == 7200000);
    assert(costMultiplier == "1.5");
    std::cout << "✓ Extended configuration features work correctly" << std::endl;

    // 测试默认值
    bool defaultBool = guild->GetConfigBool("NonExistent.Key", false);
    int defaultInt = guild->GetConfigInt("NonExistent.Key", 999);
    std::string defaultString = guild->GetConfigString("NonExistent.Key", "default");

    assert(defaultBool == false);
    assert(defaultInt == 999);
    assert(defaultString == "default");
    std::cout << "✓ Guild configuration default values work correctly" << std::endl;
}

void TestGuildFileOperations() {
    std::cout << "\n=== Testing Guild File Operations ===" << std::endl;

    auto& guildManager = GuildManager::GetInstance();
    Guild* guild = guildManager.FindGuild("TestGuild");
    assert(guild != nullptr);

    // 测试保存
    bool result = guild->SaveToFile();
    assert(result == true);
    std::cout << "✓ Guild file saved successfully" << std::endl;

    // 测试加载
    result = guild->LoadFromFile();
    assert(result == true);
    std::cout << "✓ Guild file loaded successfully" << std::endl;
}

int main() {
    std::cout << "Starting Guild System Tests..." << std::endl;

    try {
        TestGuildCreation();
        TestGuildMembers();
        TestGuildDonation();
        TestGuildLeveling();
        TestGuildSkills();
        TestGuildWarehouse();
        TestGuildRanking();
        TestGuildConfiguration();
        TestGuildFileOperations();

        std::cout << "\n=== All Guild System Tests Passed! ===" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cout << "\n=== Test Failed: " << e.what() << " ===" << std::endl;
        return 1;
    }
}
