#include "GuildManager.h"
#include "GameEngine.h"
#include "../Common/Utils.h"
#include "../Common/Logger.h"
#include "../Protocol/PacketTypes.h"
#include <algorithm>
#include <sstream>
#include <fstream>
#include <filesystem>

namespace MirServer {

// ============================================================================
// Guild 类实现
// ============================================================================

Guild::Guild(const std::string& name) : m_guildName(name) {
    m_lastSaveTime = GetCurrentTime();
    Logger::Info("Guild created: " + name);
}

Guild::~Guild() {
    // 保存数据
    if (m_changed) {
        SaveToFile();
    }
    Logger::Info("Guild destroyed: " + m_guildName);
}

const std::string& Guild::GetChiefName() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 查找会长
    for (const auto& member : m_members) {
        if (member.rank == GuildRank::CHIEF) {
            return member.playerName;
        }
    }

    static std::string empty;
    return empty;
}

int Guild::GetMemberCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return static_cast<int>(m_members.size());
}

bool Guild::IsFull() const {
    return GetMemberCount() >= MAX_GUILD_MEMBERS;
}

void Guild::SetBuildPoint(int point) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_buildPoint = point;
    m_changed = true;
}

void Guild::SetAurae(int point) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_aurae = point;
    m_changed = true;
}

void Guild::SetStability(int point) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_stability = point;
    m_changed = true;
}

void Guild::SetFlourishing(int point) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_flourishing = point;
    m_changed = true;
}

void Guild::SetChiefItemCount(int count) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_chiefItemCount = count;
    m_changed = true;
}

void Guild::SetGuildLevel(int level) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_guildLevel = std::max(1, level);
    m_changed = true;
}

void Guild::SetGuildExp(int exp) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_guildExp = std::max(0, exp);
    m_changed = true;
}

void Guild::SetGuildGold(int gold) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_guildGold = std::max(0, gold);
    m_changed = true;
}

void Guild::SetGuildRanking(int ranking) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_guildRanking = ranking;
    m_changed = true;
}

bool Guild::AddMember(PlayObject* player, GuildRank rank) {
    if (!player) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否已满员
    if (IsFull()) {
        return false;
    }

    // 检查是否已经是成员
    if (IsMember(player->GetCharName())) {
        return false;
    }

    // 添加成员
    GuildMember member(player->GetCharName(), rank);
    member.playerObject = player;
    member.isOnline = true;
    member.lastOnlineTime = GetCurrentTime();

    // 设置职位名称
    switch (rank) {
        case GuildRank::CHIEF:
            member.rankName = "会长";
            break;
        case GuildRank::VICE_CHIEF:
            member.rankName = "副会长";
            break;
        case GuildRank::CAPTAIN:
            member.rankName = "队长";
            break;
        case GuildRank::MEMBER:
        default:
            member.rankName = "成员";
            break;
    }

    m_members.push_back(member);

    // 更新玩家的行会信息
    auto& humData = const_cast<HumDataInfo&>(player->GetHumDataInfo());
    humData.guildName = m_guildName;
    humData.guildRank = static_cast<BYTE>(rank);

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    // 发送行会消息
    SendGuildMessage(player->GetCharName() + " 加入了行会");

    Logger::Info("Player " + player->GetCharName() + " joined guild " + m_guildName);
    return true;
}

bool Guild::RemoveMember(const std::string& playerName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });

    if (it == m_members.end()) {
        return false;
    }

    // 如果玩家在线，清除其行会信息
    if (it->playerObject && it->isOnline) {
        auto& humData = const_cast<HumDataInfo&>(it->playerObject->GetHumDataInfo());
        humData.guildName.clear();
        humData.guildRank = 0;

        // 通知玩家行会变化
        it->playerObject->SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);
    }

    // 发送行会消息
    SendGuildMessage(playerName + " 离开了行会");

    // 移除成员
    m_members.erase(it);

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    Logger::Info("Player " + playerName + " left guild " + m_guildName);
    return true;
}

bool Guild::IsMember(const std::string& playerName) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    return std::any_of(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });
}

GuildMember* Guild::FindMember(const std::string& playerName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });

    return (it != m_members.end()) ? &(*it) : nullptr;
}

bool Guild::UpdateMemberRank(const std::string& playerName, GuildRank newRank, const std::string& rankName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });

    if (it == m_members.end()) {
        return false;
    }

    // 更新职位
    it->rank = newRank;
    it->rankName = rankName;

    // 如果玩家在线，更新其行会信息
    if (it->playerObject && it->isOnline) {
        auto& humData = const_cast<HumDataInfo&>(it->playerObject->GetHumDataInfo());
        humData.guildRank = static_cast<BYTE>(newRank);

        // 通知玩家职位变化
        it->playerObject->SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);
    }

    // 标记需要保存
    m_changed = true;

    Logger::Info("Updated rank for " + playerName + " in guild " + m_guildName + " to " + rankName);
    return true;
}

std::string Guild::GetRankName(const std::string& playerName) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });

    return (it != m_members.end()) ? it->rankName : "";
}

GuildRank Guild::GetMemberRank(const std::string& playerName) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });

    return (it != m_members.end()) ? it->rank : GuildRank::MEMBER;
}

bool Guild::StartWar(Guild* targetGuild, DWORD duration) {
    if (!targetGuild || targetGuild == this) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否已经在战争中
    if (IsWarWith(targetGuild)) {
        return false;
    }

    // 检查是否是联盟
    if (IsAlly(targetGuild)) {
        return false;
    }

    // 创建战争信息
    GuildWarInfo warInfo(m_guildName, targetGuild->GetGuildName(), duration);
    m_guildWars.push_back(warInfo);

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    // 发送战争开始消息
    std::string message = "与 " + targetGuild->GetGuildName() + " 的行会战争开始！";
    SendGuildMessage(message);

    Logger::Info("Guild war started between " + m_guildName + " and " + targetGuild->GetGuildName());
    return true;
}

bool Guild::EndWar(Guild* targetGuild) {
    if (!targetGuild) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guildWars.begin(), m_guildWars.end(),
        [targetGuild](const GuildWarInfo& war) {
            return war.isActive &&
                   (war.guild1 == targetGuild->GetGuildName() ||
                    war.guild2 == targetGuild->GetGuildName());
        });

    if (it == m_guildWars.end()) {
        return false;
    }

    // 结束战争
    it->isActive = false;

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    // 发送战争结束消息
    std::string message = "与 " + targetGuild->GetGuildName() + " 的行会战争结束！";
    SendGuildMessage(message);

    Logger::Info("Guild war ended between " + m_guildName + " and " + targetGuild->GetGuildName());
    return true;
}

bool Guild::IsWarWith(Guild* guild) const {
    if (!guild) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    return std::any_of(m_guildWars.begin(), m_guildWars.end(),
        [guild](const GuildWarInfo& war) {
            return war.isActive &&
                   (war.guild1 == guild->GetGuildName() ||
                    war.guild2 == guild->GetGuildName());
        });
}

bool Guild::IsNotWarGuild(Guild* guild) const {
    return !IsWarWith(guild);
}

bool Guild::AddAlly(Guild* guild) {
    if (!guild || guild == this) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否已经是联盟
    if (IsAlly(guild)) {
        return false;
    }

    // 检查是否在战争中
    if (IsWarWith(guild)) {
        return false;
    }

    // 添加联盟
    GuildAllyInfo allyInfo(guild->GetGuildName());
    m_guildAllies.push_back(allyInfo);

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    // 发送联盟消息
    std::string message = "与 " + guild->GetGuildName() + " 结成联盟！";
    SendGuildMessage(message);

    Logger::Info("Guild alliance formed between " + m_guildName + " and " + guild->GetGuildName());
    return true;
}

bool Guild::RemoveAlly(Guild* guild) {
    if (!guild) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guildAllies.begin(), m_guildAllies.end(),
        [guild](const GuildAllyInfo& ally) {
            return ally.guildName == guild->GetGuildName();
        });

    if (it == m_guildAllies.end()) {
        return false;
    }

    // 移除联盟
    m_guildAllies.erase(it);

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    // 发送解除联盟消息
    std::string message = "与 " + guild->GetGuildName() + " 解除联盟！";
    SendGuildMessage(message);

    Logger::Info("Guild alliance removed between " + m_guildName + " and " + guild->GetGuildName());
    return true;
}

bool Guild::IsAlly(Guild* guild) const {
    if (!guild) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    return std::any_of(m_guildAllies.begin(), m_guildAllies.end(),
        [guild](const GuildAllyInfo& ally) {
            return ally.isActive && ally.guildName == guild->GetGuildName();
        });
}

void Guild::AddNotice(const std::string& notice) {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 添加公告到列表开头
    m_notices.insert(m_notices.begin(), notice);

    // 限制公告数量
    if (m_notices.size() > MAX_NOTICES) {
        m_notices.resize(MAX_NOTICES);
    }

    // 标记需要保存
    m_changed = true;

    Logger::Info("Notice added to guild " + m_guildName + ": " + notice);
}

void Guild::ClearNotices() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_notices.clear();
    m_changed = true;

    Logger::Info("Notices cleared for guild " + m_guildName);
}

void Guild::SendGuildMessage(const std::string& message) {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 向所有在线成员发送消息
    for (auto& member : m_members) {
        if (member.isOnline && member.playerObject) {
            member.playerObject->SendMessage("[行会] " + message, 0);
        }
    }

    Logger::Debug("Guild message sent to " + m_guildName + ": " + message);
}

void Guild::SendGuildNotice(const std::string& notice) {
    AddNotice(notice);
    SendGuildMessage("公告: " + notice);
}

void Guild::UpdateGuildFile() {
    m_changed = true;
    m_lastSaveTime = GetCurrentTime();
    SaveToFile();
}

void Guild::CheckSaveGuildFile() {
    if (m_changed && (GetCurrentTime() - m_lastSaveTime) > SAVE_INTERVAL) {
        m_changed = false;
        SaveToFile();
    }
}

void Guild::OnPlayerLogin(PlayObject* player) {
    if (!player) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [player](GuildMember& member) {
            return member.playerName == player->GetCharName();
        });

    if (it != m_members.end()) {
        it->playerObject = player;
        it->isOnline = true;
        it->lastOnlineTime = GetCurrentTime();

        // 更新玩家的行会信息
        auto& humData = const_cast<HumDataInfo&>(player->GetHumDataInfo());
        humData.guildName = m_guildName;
        humData.guildRank = static_cast<BYTE>(it->rank);

        // 通知玩家行会信息
        player->SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);

        Logger::Debug("Guild member " + player->GetCharName() + " logged in to guild " + m_guildName);
    }
}

void Guild::OnPlayerLogout(PlayObject* player) {
    if (!player) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [player](GuildMember& member) {
            return member.playerName == player->GetCharName();
        });

    if (it != m_members.end()) {
        it->playerObject = nullptr;
        it->isOnline = false;
        it->lastOnlineTime = GetCurrentTime();

        Logger::Debug("Guild member " + player->GetCharName() + " logged out from guild " + m_guildName);
    }
}

void Guild::StartTeamFight() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_teamFightActive = true;
    m_contestPoint = 0;
    m_teamFightDeadList.clear();

    SendGuildMessage("团队战开始！");
    Logger::Info("Team fight started for guild " + m_guildName);
}

void Guild::EndTeamFight() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_teamFightActive = false;

    SendGuildMessage("团队战结束！最终积分: " + std::to_string(m_contestPoint));
    Logger::Info("Team fight ended for guild " + m_guildName + " with score " + std::to_string(m_contestPoint));
}

void Guild::AddTeamFightMember(const std::string& playerName) {
    if (!m_teamFightActive) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find(m_teamFightDeadList.begin(), m_teamFightDeadList.end(), playerName);
    if (it == m_teamFightDeadList.end()) {
        m_teamFightDeadList.push_back(playerName);
    }
}

void Guild::TeamFightWhoDead(const std::string& playerName) {
    if (!m_teamFightActive) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // TODO: 实现团队战死亡统计
    Logger::Debug("Team fight death recorded for " + playerName + " in guild " + m_guildName);
}

void Guild::TeamFightWhoWinPoint(const std::string& playerName, int point) {
    if (!m_teamFightActive) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    m_contestPoint += point;

    Logger::Debug("Team fight points added: " + std::to_string(point) + " for " + playerName + " in guild " + m_guildName);
}

void Guild::Run() {
    std::lock_guard<std::mutex> lock(m_mutex);

    DWORD currentTime = GetCurrentTime();
    bool warChanged = false;

    // 检查战争是否过期
    for (auto& war : m_guildWars) {
        if (war.isActive && (currentTime - war.startTime) > war.duration) {
            war.isActive = false;
            warChanged = true;

            // 发送战争结束消息
            std::string targetGuild = (war.guild1 == m_guildName) ? war.guild2 : war.guild1;
            SendGuildMessage("与 " + targetGuild + " 的行会战争时间到期！");
        }
    }

    if (warChanged) {
        m_changed = true;
        UpdateGuildFile();
    }

    // 检查是否需要保存
    CheckSaveGuildFile();
}

// ============================================================================
// Guild 捐献系统实现
// ============================================================================

bool Guild::DonateGold(PlayObject* player, int amount) {
    if (!player || amount <= 0) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查玩家是否有足够金币
    if (player->GetGold() < amount) {
        return false;
    }

    // 扣除玩家金币
    player->DecGold(amount);

    // 增加行会金币
    m_guildGold += amount;

    // 记录捐献
    AddDonationRecord(player->GetCharName(), amount);

    // 增加行会经验
    AddGuildExp(amount / 100); // 每100金币获得1经验

    m_changed = true;
    Logger::Info("Player " + player->GetCharName() + " donated " + std::to_string(amount) + " gold to guild " + m_guildName);
    return true;
}

bool Guild::DonateItem(PlayObject* player, const std::string& itemName, int count) {
    if (!player || itemName.empty() || count <= 0) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // TODO: 检查玩家是否有该物品
    // 这里需要实现物品检查逻辑

    // 记录捐献
    AddDonationRecord(player->GetCharName(), 0, itemName, count);

    // 增加到行会仓库
    m_warehouseItems[itemName] += count;

    // 增加行会经验
    AddGuildExp(count * 10); // 每个物品获得10经验

    m_changed = true;
    Logger::Info("Player " + player->GetCharName() + " donated " + std::to_string(count) + " " + itemName + " to guild " + m_guildName);
    return true;
}

int Guild::GetTotalDonation(const std::string& playerName) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_memberDonations.find(playerName);
    return (it != m_memberDonations.end()) ? it->second : 0;
}

void Guild::AddDonationRecord(const std::string& playerName, int gold, const std::string& item, int count) {
    // 更新总捐献记录
    m_memberDonations[playerName] += gold;

    // 添加到历史记录
    std::string record = playerName + " 捐献了 ";
    if (gold > 0) {
        record += std::to_string(gold) + " 金币";
    }
    if (!item.empty() && count > 0) {
        if (gold > 0) record += " 和 ";
        record += std::to_string(count) + " " + item;
    }

    m_donationHistory.push_back(record);

    // 保持历史记录不超过100条
    if (m_donationHistory.size() > 100) {
        m_donationHistory.erase(m_donationHistory.begin());
    }
}

// ============================================================================
// Guild 升级系统实现
// ============================================================================

bool Guild::CanLevelUp() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_guildExp >= GetRequiredExpForNextLevel();
}

bool Guild::LevelUp() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!CanLevelUp()) {
        return false;
    }

    int requiredExp = GetRequiredExpForNextLevel();
    m_guildExp -= requiredExp;
    m_guildLevel++;

    // 升级奖励
    m_buildPoint += 100;
    m_aurae += 50;
    m_stability += 50;
    m_flourishing += 50;

    m_changed = true;

    // 通知所有成员
    SendGuildMessage("行会升级到 " + std::to_string(m_guildLevel) + " 级！");

    Logger::Info("Guild " + m_guildName + " leveled up to " + std::to_string(m_guildLevel));
    return true;
}

int Guild::GetRequiredExpForNextLevel() const {
    // 升级所需经验公式：level * 1000
    return m_guildLevel * 1000;
}

void Guild::AddGuildExp(int exp) {
    if (exp <= 0) return;

    std::lock_guard<std::mutex> lock(m_mutex);
    m_guildExp += exp;

    // 检查是否可以升级
    while (CanLevelUp()) {
        LevelUp();
    }

    m_changed = true;
}

bool Guild::SetGuildInfo(const std::string& chiefName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_members.empty()) {
        // 创建会长成员
        GuildMember chief(chiefName, GuildRank::CHIEF);
        chief.rankName = "会长";
        m_members.push_back(chief);

        m_changed = true;
        SaveToFile();

        Logger::Info("Guild chief set for " + m_guildName + ": " + chiefName);
        return true;
    }

    return false;
}

// ============================================================================
// Guild 排名系统实现
// ============================================================================

void Guild::UpdateRanking() {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_rankingScore = CalculateRankingScore();
    m_changed = true;
}

int Guild::CalculateRankingScore() const {
    // 排名分数计算公式：
    // 基础分数 = 行会等级 * 100 + 成员数量 * 10 + 建设度 + 灵气值 + 安定度 + 繁荣度
    int baseScore = m_guildLevel * 100 + GetMemberCount() * 10;
    int propertyScore = m_buildPoint + m_aurae + m_stability + m_flourishing;

    // 战争胜利加分（假设有胜利记录）
    int warBonus = 0;
    for (const auto& war : m_guildWars) {
        if (war.isActive) {
            warBonus += 50; // 每个活跃战争加50分
        }
    }

    return baseScore + propertyScore + warBonus;
}

void Guild::SetRankingInfo(int ranking, int score) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_guildRanking = ranking;
    m_rankingScore = score;
    m_changed = true;
}

// ============================================================================
// Guild 仓库系统实现
// ============================================================================

bool Guild::DepositItem(PlayObject* player, const std::string& itemName, int count) {
    if (!player || itemName.empty() || count <= 0) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查权限
    if (!CanAccessWarehouse(player)) {
        return false;
    }

    // TODO: 检查玩家是否有该物品
    // 这里需要实现物品检查逻辑

    // 添加到仓库
    m_warehouseItems[itemName] += count;

    m_changed = true;
    Logger::Info("Player " + player->GetCharName() + " deposited " + std::to_string(count) + " " + itemName + " to guild warehouse");
    return true;
}

bool Guild::WithdrawItem(PlayObject* player, const std::string& itemName, int count) {
    if (!player || itemName.empty() || count <= 0) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查权限
    if (!CanAccessWarehouse(player)) {
        return false;
    }

    // 检查仓库是否有足够物品
    auto it = m_warehouseItems.find(itemName);
    if (it == m_warehouseItems.end() || it->second < count) {
        return false;
    }

    // 从仓库移除
    it->second -= count;
    if (it->second <= 0) {
        m_warehouseItems.erase(it);
    }

    // TODO: 给玩家添加物品
    // 这里需要实现物品添加逻辑

    m_changed = true;
    Logger::Info("Player " + player->GetCharName() + " withdrew " + std::to_string(count) + " " + itemName + " from guild warehouse");
    return true;
}

bool Guild::CanAccessWarehouse(PlayObject* player) const {
    if (!player) {
        return false;
    }

    // 只有会长、副会长和队长可以访问仓库
    GuildRank rank = GetMemberRank(player->GetCharName());
    return rank == GuildRank::CHIEF || rank == GuildRank::VICE_CHIEF || rank == GuildRank::CAPTAIN;
}

std::vector<std::string> Guild::GetWarehouseItems() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    std::vector<std::string> items;
    for (const auto& item : m_warehouseItems) {
        items.push_back(item.first + " x" + std::to_string(item.second));
    }

    return items;
}

// ============================================================================
// Guild 技能系统实现
// ============================================================================

bool Guild::LearnGuildSkill(const std::string& skillName) {
    if (skillName.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否已经学会
    if (m_guildSkills.find(skillName) != m_guildSkills.end()) {
        return false;
    }

    // 检查行会等级要求（简单实现）
    if (m_guildLevel < 2) {
        return false;
    }

    // 检查金币要求
    int requiredGold = 10000; // 学习技能需要10000金币
    if (m_guildGold < requiredGold) {
        return false;
    }

    // 扣除金币
    m_guildGold -= requiredGold;

    // 学会技能
    m_guildSkills[skillName] = 1;

    m_changed = true;
    SendGuildMessage("行会学会了新技能: " + skillName);
    Logger::Info("Guild " + m_guildName + " learned skill: " + skillName);
    return true;
}

bool Guild::UpgradeGuildSkill(const std::string& skillName) {
    if (skillName.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_guildSkills.find(skillName);
    if (it == m_guildSkills.end()) {
        return false; // 技能未学会
    }

    if (it->second >= 10) {
        return false; // 已达到最大等级
    }

    // 检查金币要求
    int requiredGold = (it->second + 1) * 5000; // 升级费用递增
    if (m_guildGold < requiredGold) {
        return false;
    }

    // 扣除金币
    m_guildGold -= requiredGold;

    // 升级技能
    it->second++;

    m_changed = true;
    SendGuildMessage("行会技能 " + skillName + " 升级到 " + std::to_string(it->second) + " 级");
    Logger::Info("Guild " + m_guildName + " upgraded skill " + skillName + " to level " + std::to_string(it->second));
    return true;
}

int Guild::GetGuildSkillLevel(const std::string& skillName) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_guildSkills.find(skillName);
    return (it != m_guildSkills.end()) ? it->second : 0;
}

std::vector<std::string> Guild::GetAvailableSkills() const {
    // 返回可学习的技能列表
    return {
        "行会攻击力提升",
        "行会防御力提升",
        "行会经验加成",
        "行会金币加成",
        "行会传送",
        "行会复活",
        "行会治疗",
        "行会护盾"
    };
}

// ============================================================================
// Guild 领地系统实现
// ============================================================================

bool Guild::ClaimTerritory(const std::string& territoryName) {
    if (territoryName.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否已经拥有该领地
    auto it = std::find(m_territories.begin(), m_territories.end(), territoryName);
    if (it != m_territories.end()) {
        return false;
    }

    // 检查行会等级要求
    if (m_guildLevel < 3) {
        return false;
    }

    // 添加领地
    m_territories.push_back(territoryName);

    m_changed = true;
    SendGuildMessage("行会占领了领地: " + territoryName);
    Logger::Info("Guild " + m_guildName + " claimed territory: " + territoryName);
    return true;
}

bool Guild::LoseTerritory(const std::string& territoryName) {
    if (territoryName.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find(m_territories.begin(), m_territories.end(), territoryName);
    if (it == m_territories.end()) {
        return false;
    }

    m_territories.erase(it);

    m_changed = true;
    SendGuildMessage("行会失去了领地: " + territoryName);
    Logger::Info("Guild " + m_guildName + " lost territory: " + territoryName);
    return true;
}

std::vector<std::string> Guild::GetTerritories() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_territories;
}

int Guild::GetTerritoryIncome() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 每个领地每天产生1000金币收入
    return static_cast<int>(m_territories.size()) * 1000;
}

// ============================================================================
// Guild 文件操作实现
// ============================================================================

bool Guild::LoadFromFile() {
    std::string fileName = m_guildName + ".txt";
    std::string configFileName = m_guildName + ".ini";

    bool result = LoadGuildFile(fileName);
    LoadGuildConfig(configFileName);

    return result;
}

bool Guild::SaveToFile() {
    std::string fileName = m_guildName + ".txt";
    std::string configFileName = m_guildName + ".ini";

    SaveGuildFile(fileName);
    SaveGuildConfig(configFileName);

    return true;
}

void Guild::BackupGuildFile() {
    std::string backupFileName = m_guildName + "." + std::to_string(GetCurrentTime()) + ".bak";
    SaveGuildFile(backupFileName);

    Logger::Info("Guild file backed up: " + backupFileName);
}

bool Guild::LoadGuildFile(const std::string& fileName) {
    std::string fullPath = "GuildBase/" + fileName;

    std::ifstream file(fullPath);
    if (!file.is_open()) {
        Logger::Warning("Guild file not found: " + fullPath);
        return false;
    }

    std::string line;
    int lineNum = 0;

    try {
        while (std::getline(file, line)) {
            lineNum++;
            line = Trim(line);

            if (line.empty() || line[0] == '#') {
                continue; // 跳过空行和注释
            }

            // 解析行会基本信息
            if (lineNum == 1) {
                // 第一行：行会名称（验证）
                if (line != m_guildName) {
                    Logger::Error("Guild name mismatch in file: " + fileName);
                    file.close();
                    return false;
                }
            }
            else if (lineNum == 2) {
                // 第二行：行会属性 (level,exp,gold,buildPoint,aurae,stability,flourishing)
                std::vector<std::string> parts = Split(line, ',');
                if (parts.size() >= 7) {
                    m_guildLevel = std::stoi(parts[0]);
                    m_guildExp = std::stoi(parts[1]);
                    m_guildGold = std::stoi(parts[2]);
                    m_buildPoint = std::stoi(parts[3]);
                    m_aurae = std::stoi(parts[4]);
                    m_stability = std::stoi(parts[5]);
                    m_flourishing = std::stoi(parts[6]);
                }
            }
            else if (line.find("MEMBER:") == 0) {
                // 成员信息：MEMBER:playerName,rank,rankName,joinTime,lastOnlineTime
                std::string memberData = line.substr(7); // 去掉"MEMBER:"
                std::vector<std::string> parts = Split(memberData, ',');
                if (parts.size() >= 5) {
                    GuildMember member;
                    member.playerName = parts[0];
                    member.rank = static_cast<GuildRank>(std::stoi(parts[1]));
                    member.rankName = parts[2];
                    member.joinTime = std::stoul(parts[3]);
                    member.lastOnlineTime = std::stoul(parts[4]);
                    member.isOnline = false; // 加载时默认离线
                    member.playerObject = nullptr;

                    m_members.push_back(member);
                }
            }
            else if (line.find("NOTICE:") == 0) {
                // 公告信息：NOTICE:公告内容
                std::string notice = line.substr(7);
                if (!notice.empty()) {
                    m_notices.push_back(notice);
                }
            }
            else if (line.find("WAR:") == 0) {
                // 战争信息：WAR:guild1,guild2,startTime,duration,isActive
                std::string warData = line.substr(4);
                std::vector<std::string> parts = Split(warData, ',');
                if (parts.size() >= 5) {
                    GuildWarInfo war;
                    war.guild1 = parts[0];
                    war.guild2 = parts[1];
                    war.startTime = std::stoul(parts[2]);
                    war.duration = std::stoul(parts[3]);
                    war.isActive = (parts[4] == "1");

                    m_guildWars.push_back(war);
                }
            }
            else if (line.find("ALLY:") == 0) {
                // 联盟信息：ALLY:guildName,allyTime,isActive
                std::string allyData = line.substr(5);
                std::vector<std::string> parts = Split(allyData, ',');
                if (parts.size() >= 3) {
                    GuildAllyInfo ally;
                    ally.guildName = parts[0];
                    ally.allyTime = std::stoul(parts[1]);
                    ally.isActive = (parts[2] == "1");

                    m_guildAllies.push_back(ally);
                }
            }
            else if (line.find("DONATION:") == 0) {
                // 捐献记录：DONATION:playerName,amount
                std::string donationData = line.substr(9);
                std::vector<std::string> parts = Split(donationData, ',');
                if (parts.size() >= 2) {
                    m_memberDonations[parts[0]] = std::stoi(parts[1]);
                }
            }
            else if (line.find("WAREHOUSE:") == 0) {
                // 仓库物品：WAREHOUSE:itemName,count
                std::string warehouseData = line.substr(10);
                std::vector<std::string> parts = Split(warehouseData, ',');
                if (parts.size() >= 2) {
                    m_warehouseItems[parts[0]] = std::stoi(parts[1]);
                }
            }
            else if (line.find("SKILL:") == 0) {
                // 技能信息：SKILL:skillName,level
                std::string skillData = line.substr(6);
                std::vector<std::string> parts = Split(skillData, ',');
                if (parts.size() >= 2) {
                    m_guildSkills[parts[0]] = std::stoi(parts[1]);
                }
            }
            else if (line.find("TERRITORY:") == 0) {
                // 领地信息：TERRITORY:territoryName
                std::string territory = line.substr(10);
                if (!territory.empty()) {
                    m_territories.push_back(territory);
                }
            }
        }

        file.close();
        Logger::Info("Successfully loaded guild file: " + fileName);
        return true;
    }
    catch (const std::exception& e) {
        file.close();
        Logger::Error("Error loading guild file " + fileName + ": " + e.what());
        return false;
    }
}

bool Guild::LoadGuildConfig(const std::string& fileName) {
    // TODO: 实现从配置文件加载行会配置
    Logger::Info("Loading guild config: " + fileName);
    return true;
}

void Guild::SaveGuildFile(const std::string& fileName) {
    std::string fullPath = "GuildBase/" + fileName;

    // 确保目录存在
    std::filesystem::create_directories("GuildBase");

    std::ofstream file(fullPath);
    if (!file.is_open()) {
        Logger::Error("Failed to save guild file: " + fullPath);
        return;
    }

    try {
        std::lock_guard<std::mutex> lock(m_mutex);

        // 写入文件头注释
        file << "# Guild Data File for " << m_guildName << std::endl;
        file << "# Generated at " << GetCurrentTime() << std::endl;
        file << std::endl;

        // 第一行：行会名称
        file << m_guildName << std::endl;

        // 第二行：行会属性 (level,exp,gold,buildPoint,aurae,stability,flourishing)
        file << m_guildLevel << "," << m_guildExp << "," << m_guildGold << ","
             << m_buildPoint << "," << m_aurae << "," << m_stability << "," << m_flourishing << std::endl;

        // 成员信息
        for (const auto& member : m_members) {
            file << "MEMBER:" << member.playerName << ","
                 << static_cast<int>(member.rank) << ","
                 << member.rankName << ","
                 << member.joinTime << ","
                 << member.lastOnlineTime << std::endl;
        }

        // 公告信息
        for (const auto& notice : m_notices) {
            file << "NOTICE:" << notice << std::endl;
        }

        // 战争信息
        for (const auto& war : m_guildWars) {
            file << "WAR:" << war.guild1 << "," << war.guild2 << ","
                 << war.startTime << "," << war.duration << ","
                 << (war.isActive ? "1" : "0") << std::endl;
        }

        // 联盟信息
        for (const auto& ally : m_guildAllies) {
            file << "ALLY:" << ally.guildName << "," << ally.allyTime << ","
                 << (ally.isActive ? "1" : "0") << std::endl;
        }

        // 捐献记录
        for (const auto& donation : m_memberDonations) {
            file << "DONATION:" << donation.first << "," << donation.second << std::endl;
        }

        // 仓库物品
        for (const auto& item : m_warehouseItems) {
            file << "WAREHOUSE:" << item.first << "," << item.second << std::endl;
        }

        // 技能信息
        for (const auto& skill : m_guildSkills) {
            file << "SKILL:" << skill.first << "," << skill.second << std::endl;
        }

        // 领地信息
        for (const auto& territory : m_territories) {
            file << "TERRITORY:" << territory << std::endl;
        }

        file.close();
        m_lastSaveTime = GetCurrentTime();
        Logger::Info("Successfully saved guild file: " + fileName);
    }
    catch (const std::exception& e) {
        file.close();
        Logger::Error("Error saving guild file " + fileName + ": " + e.what());
    }
}

void Guild::SaveGuildConfig(const std::string& fileName) {
    // TODO: 实现保存行会配置到文件
    Logger::Info("Saving guild config: " + fileName);
}

void Guild::ClearRanks() {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_members.clear();
}

void Guild::RefreshMemberNames() {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 刷新所有在线成员的显示名称
    for (auto& member : m_members) {
        if (member.isOnline && member.playerObject) {
            member.playerObject->SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);
        }
    }
}

// ============================================================================
// GuildManager 类实现
// ============================================================================

GuildManager& GuildManager::GetInstance() {
    static GuildManager instance;
    return instance;
}

bool GuildManager::Initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized) {
        return true;
    }

    // 设置配置路径
    m_guildListFile = "GuildList.txt";
    m_guildDir = "GuildBase/";

    // 创建行会目录
    std::filesystem::create_directories(m_guildDir);

    // 清理现有数据
    ClearGuildInfo();

    // 加载行会信息
    LoadGuildInfo();

    m_initialized = true;
    Logger::Info("GuildManager initialized successfully");
    return true;
}

void GuildManager::Finalize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return;
    }

    // 保存所有行会数据
    for (auto& guild : m_guilds) {
        if (guild) {
            guild->SaveToFile();
        }
    }

    // 保存行会列表
    SaveGuildList();

    // 清理数据
    ClearGuildInfo();

    m_initialized = false;
    Logger::Info("GuildManager finalized");
}

bool GuildManager::CreateGuild(const std::string& guildName, PlayObject* chief) {
    if (!chief || guildName.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查行会名称是否有效
    if (guildName.length() > 20 || guildName.length() < 2) {
        return false;
    }

    // 检查行会是否已存在
    if (FindGuild(guildName)) {
        return false;
    }

    // 检查玩家是否已经有行会
    if (GetPlayerGuild(chief->GetCharName())) {
        return false;
    }

    // 创建新行会
    auto guild = std::make_unique<Guild>(guildName);
    if (!guild->SetGuildInfo(chief->GetCharName())) {
        return false;
    }

    // 添加会长为成员
    if (!guild->AddMember(chief, GuildRank::CHIEF)) {
        return false;
    }

    // 添加到行会列表
    m_guilds.push_back(std::move(guild));

    // 保存行会列表
    SaveGuildList();

    Logger::Info("Guild created: " + guildName + " by " + chief->GetCharName());
    return true;
}

bool GuildManager::DeleteGuild(const std::string& guildName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guilds.begin(), m_guilds.end(),
        [&guildName](const std::unique_ptr<Guild>& guild) {
            return guild && guild->GetGuildName() == guildName;
        });

    if (it == m_guilds.end()) {
        return false;
    }

    // 检查是否只有会长一人
    if ((*it)->GetMemberCount() > 1) {
        return false;
    }

    // 备份行会文件
    (*it)->BackupGuildFile();

    // 移除行会
    m_guilds.erase(it);

    // 保存行会列表
    SaveGuildList();

    Logger::Info("Guild deleted: " + guildName);
    return true;
}

Guild* GuildManager::FindGuild(const std::string& guildName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guilds.begin(), m_guilds.end(),
        [&guildName](const std::unique_ptr<Guild>& guild) {
            return guild && guild->GetGuildName() == guildName;
        });

    return (it != m_guilds.end()) ? it->get() : nullptr;
}

Guild* GuildManager::GetPlayerGuild(const std::string& playerName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    for (auto& guild : m_guilds) {
        if (guild && guild->IsMember(playerName)) {
            return guild.get();
        }
    }

    return nullptr;
}

void GuildManager::LoadGuildInfo() {
    if (!std::filesystem::exists(m_guildListFile)) {
        Logger::Info("Guild list file not found, starting with empty guild list");
        return;
    }

    std::ifstream file(m_guildListFile);
    if (!file.is_open()) {
        Logger::Error("Failed to open guild list file: " + m_guildListFile);
        return;
    }

    std::string guildName;
    int loadedCount = 0;

    while (std::getline(file, guildName)) {
        guildName = Trim(guildName);
        if (guildName.empty()) {
            continue;
        }

        auto guild = std::make_unique<Guild>(guildName);
        if (guild->LoadFromFile()) {
            m_guilds.push_back(std::move(guild));
            loadedCount++;
        } else {
            Logger::Warning("Failed to load guild: " + guildName);
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(loadedCount) + " guilds");
}

void GuildManager::SaveGuildList() {
    std::ofstream file(m_guildListFile);
    if (!file.is_open()) {
        Logger::Error("Failed to save guild list file: " + m_guildListFile);
        return;
    }

    for (const auto& guild : m_guilds) {
        if (guild) {
            file << guild->GetGuildName() << std::endl;
        }
    }

    file.close();
    Logger::Debug("Guild list saved");
}

// ============================================================================
// GuildManager 排名系统实现
// ============================================================================

void GuildManager::UpdateAllGuildRankings() {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 更新所有行会的排名分数
    for (auto& guild : m_guilds) {
        if (guild) {
            guild->UpdateRanking();
        }
    }

    // 按排名分数排序
    std::sort(m_guilds.begin(), m_guilds.end(),
        [](const std::unique_ptr<Guild>& a, const std::unique_ptr<Guild>& b) {
            if (!a) return false;
            if (!b) return true;
            return a->CalculateRankingScore() > b->CalculateRankingScore();
        });

    // 设置排名
    for (size_t i = 0; i < m_guilds.size(); ++i) {
        if (m_guilds[i]) {
            m_guilds[i]->SetRankingInfo(static_cast<int>(i + 1), m_guilds[i]->CalculateRankingScore());
        }
    }

    Logger::Info("Updated rankings for " + std::to_string(m_guilds.size()) + " guilds");
}

std::vector<Guild*> GuildManager::GetGuildRankings(int count) {
    std::lock_guard<std::mutex> lock(m_mutex);

    std::vector<Guild*> rankings;
    int maxCount = std::min(count, static_cast<int>(m_guilds.size()));

    for (int i = 0; i < maxCount; ++i) {
        if (m_guilds[i]) {
            rankings.push_back(m_guilds[i].get());
        }
    }

    return rankings;
}

int GuildManager::GetGuildRank(const std::string& guildName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    for (size_t i = 0; i < m_guilds.size(); ++i) {
        if (m_guilds[i] && m_guilds[i]->GetGuildName() == guildName) {
            return static_cast<int>(i + 1);
        }
    }

    return 0; // 未找到
}

// ============================================================================
// GuildManager 统计功能实现
// ============================================================================

int GuildManager::GetTotalGuildCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return static_cast<int>(m_guilds.size());
}

int GuildManager::GetTotalMemberCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    int totalMembers = 0;
    for (const auto& guild : m_guilds) {
        if (guild) {
            totalMembers += guild->GetMemberCount();
        }
    }

    return totalMembers;
}

Guild* GuildManager::GetTopGuild() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_guilds.empty()) {
        return nullptr;
    }

    return m_guilds[0].get(); // 第一个就是排名最高的
}

void GuildManager::Run() {
    if (!m_initialized) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 运行所有行会的定时任务
    for (auto& guild : m_guilds) {
        if (guild) {
            guild->Run();
        }
    }

    // 定期更新排名（每5分钟更新一次）
    static DWORD lastRankingUpdate = 0;
    DWORD currentTime = GetCurrentTime();
    if (currentTime - lastRankingUpdate > 300000) { // 5分钟
        UpdateAllGuildRankings();
        lastRankingUpdate = currentTime;
    }
}

void GuildManager::ClearGuildInfo() {
    m_guilds.clear();
}

} // namespace MirServer
